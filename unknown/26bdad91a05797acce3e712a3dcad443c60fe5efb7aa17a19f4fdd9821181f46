<!-- src/routes/(public)/onboarding/+page.svelte -->
<script lang="ts">
	import { superForm } from 'sveltekit-superforms';
	import { zodClient } from 'sveltekit-superforms/adapters';
	import { basicProfileSchema } from '$lib/schemas/profile.schema';
	import * as m from '$lib/paraglide/messages';
	import { page } from '$app/stores';
	import { Page, Navbar, Block, Button, List, ListItem, BlockTitle } from 'konsta/svelte';

	export let data;

	const { form, errors, constraints, enhance, message } = superForm(data.form, {
		validators: zodClient(basicProfileSchema)
	});

	// Body type labels mapping
	const bodyTypeLabels = {
		male_body: () => m.body_type_male_body(),
		female_body: () => m.body_type_female_body(),
		other_body_type: () => m.body_type_other_body_type()
	};
</script>

<Page>
	<Navbar title={m.onboarding_title()} />

	<Block>
		<p class="text-gray-600">{m.onboarding_description()}</p>
	</Block>

	<form method="POST" use:enhance>
		<List strongIos outlineIos>
			<!-- Nickname -->
			<ListItem>
				<div class="w-full">
					<label for="nickname" class="mb-1 block text-sm font-medium">{m.nickname_label()}</label>
					<input
						type="text"
						id="nickname"
						name="nickname"
						bind:value={$form.nickname}
						aria-invalid={$errors.nickname ? 'true' : undefined}
						class="w-full rounded-md border border-gray-300 px-3 py-2 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 focus:outline-none"
						data-fs-error-for="nickname"
						{...$constraints.nickname}
					/>
					{#if $errors.nickname}
						<div class="mt-1 text-sm text-red-500">{$errors.nickname}</div>
					{/if}
				</div>
			</ListItem>

			<!-- Age -->
			<ListItem>
				<div class="w-full">
					<label for="age" class="mb-1 block text-sm font-medium">{m.age_label()}</label>
					<input
						type="number"
						id="age"
						name="age"
						bind:value={$form.age}
						aria-invalid={$errors.age ? 'true' : undefined}
						class="w-full rounded-md border border-gray-300 px-3 py-2 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 focus:outline-none"
						data-fs-error-for="age"
						{...$constraints.age}
					/>
					{#if $errors.age}
						<div class="mt-1 text-sm text-red-500">{$errors.age}</div>
					{/if}
				</div>
			</ListItem>

			<!-- Height -->
			<ListItem>
				<div class="w-full">
					<label for="heightCm" class="mb-1 block text-sm font-medium">{m.height_label()}</label>
					<input
						type="number"
						id="heightCm"
						name="heightCm"
						bind:value={$form.heightCm}
						aria-invalid={$errors.heightCm ? 'true' : undefined}
						class="w-full rounded-md border border-gray-300 px-3 py-2 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 focus:outline-none"
						data-fs-error-for="heightCm"
						{...$constraints.heightCm}
					/>
					{#if $errors.heightCm}
						<div class="mt-1 text-sm text-red-500">{$errors.heightCm}</div>
					{/if}
				</div>
			</ListItem>

			<!-- Weight -->
			<ListItem>
				<div class="w-full">
					<label for="weightKg" class="mb-1 block text-sm font-medium">{m.weight_label()}</label>
					<input
						type="number"
						id="weightKg"
						name="weightKg"
						bind:value={$form.weightKg}
						aria-invalid={$errors.weightKg ? 'true' : undefined}
						class="w-full rounded-md border border-gray-300 px-3 py-2 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 focus:outline-none"
						data-fs-error-for="weightKg"
						{...$constraints.weightKg}
					/>
					{#if $errors.weightKg}
						<div class="mt-1 text-sm text-red-500">{$errors.weightKg}</div>
					{/if}
				</div>
			</ListItem>

			<!-- Body Type -->
			<ListItem>
				<div class="w-full">
					<label for="bodyType" class="mb-1 block text-sm font-medium">{m.body_type_label()}</label>
					<select
						id="bodyType"
						name="bodyType"
						bind:value={$form.bodyType}
						aria-invalid={$errors.bodyType ? 'true' : undefined}
						class="w-full rounded-md border border-gray-300 px-3 py-2 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 focus:outline-none"
						data-fs-error-for="bodyType"
						required
					>
						<option value="" disabled selected>{m.select_body_type_placeholder()}</option>
						{#each data.bodyTypes as type}
							<option value={type}>{bodyTypeLabels[type]()}</option>
						{/each}
					</select>
					{#if $errors.bodyType}
						<div class="mt-1 text-sm text-red-500">{$errors.bodyType}</div>
					{/if}
				</div>
			</ListItem>

			<!-- Country -->
			<ListItem>
				<div class="w-full">
					<label for="countryCode" class="mb-1 block text-sm font-medium">{m.country_label()}</label
					>
					<input
						type="text"
						id="countryCode"
						name="countryCode"
						bind:value={$form.countryCode}
						class="w-full rounded-md border border-gray-300 px-3 py-2 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 focus:outline-none"
						{...$constraints.countryCode}
					/>
				</div>
			</ListItem>

			<!-- Province -->
			<ListItem>
				<div class="w-full">
					<label for="provinceCode" class="mb-1 block text-sm font-medium"
						>{m.province_label()}</label
					>
					<input
						type="text"
						id="provinceCode"
						name="provinceCode"
						bind:value={$form.provinceCode}
						class="w-full rounded-md border border-gray-300 px-3 py-2 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 focus:outline-none"
						{...$constraints.provinceCode}
					/>
				</div>
			</ListItem>

			<!-- City -->
			<ListItem>
				<div class="w-full">
					<label for="city" class="mb-1 block text-sm font-medium">{m.city_label()}</label>
					<input
						type="text"
						id="city"
						name="city"
						bind:value={$form.city}
						class="w-full rounded-md border border-gray-300 px-3 py-2 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 focus:outline-none"
						{...$constraints.city}
					/>
				</div>
			</ListItem>
		</List>

		{#if $message}
			<Block>
				<div class="rounded-md bg-red-100 p-4 text-red-700">{$message}</div>
			</Block>
		{/if}

		<Block>
			<Button type="submit" large fill>
				{m.onboarding_submit_button()}
			</Button>
		</Block>
	</form>
</Page>
