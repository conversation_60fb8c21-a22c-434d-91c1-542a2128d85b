// src/lib/server/db/schema/users.ts
// CipherX TMA - 用户表定义 (包含关系)

import {
	pgTable,
	text,
	integer,
	bigint,
	boolean,
	timestamp,
	jsonb,
	type AnyPgColumn
} from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';
import { sessions, keys } from './auth'; // 导入用于定义关系的表
import { groupMemberships, groups } from './groups';
import { matches } from './social';
import { pointTransactions } from './economy';
import {
	orientationEnum,
	bodyTypeEnum,
	presentationStyleEnum,
	relationshipStatusEnum
} from './_enums';

/**
 * 用户表 - 应用的核心实体
 * 包含基础资料和高级资料的完整定义
 */
export const users = pgTable('users', {
	// 核心标识
	id: text('id').primaryKey(),
	telegramUserId: bigint('telegram_user_id', { mode: 'number' }).unique().notNull(),
	tonWalletAddress: text('ton_wallet_address').unique(),
	inviterId: text('inviter_id').references((): AnyPgColumn => users.id, { onDelete: 'set null' }),
	kinkMapCode: text('kink_map_code').unique().notNull(),

	// 基础资料 (Basic Profile)
	nickname: text('nickname').notNull(),
	telegramUsername: text('telegram_username'),
	age: integer('age'),
	heightCm: integer('height_cm'),
	weightKg: integer('weight_kg'),
	countryCode: text('country_code'),
	provinceCode: text('province_code'),
	city: text('city'),
	languageCode: text('language_code'),

	// 高级资料 (Advanced Profile)
	bio: text('bio'),
	profileImageUrl: text('profile_image_url'),
	orientation: orientationEnum('orientation'),
	bodyType: bodyTypeEnum('body_type'),
	presentationStyle: presentationStyleEnum('presentation_style'),
	relationshipStatus: relationshipStatusEnum('relationship_status'),

	// Kink 相关数据
	kinkCategoryBitmask: bigint('kink_category_bitmask', { mode: 'number' }).notNull().default(0),
	kinkRatings: jsonb('kink_ratings').notNull().default({}),

	// 系统计算字段
	profileCompletenessScore: integer('profile_completeness_score').notNull().default(0),
	trustScore: integer('trust_score').notNull().default(100),
	vipLevel: integer('vip_level').notNull().default(0),
	pointBalance: integer('point_balance').notNull().default(0),

	// 状态字段
	isActive: boolean('is_active').notNull().default(true),
	isBanned: boolean('is_banned').notNull().default(false),

	// 时间戳
	createdAt: timestamp('created_at', { withTimezone: true }).notNull().defaultNow(),
	updatedAt: timestamp('updated_at', { withTimezone: true }).notNull().defaultNow(),
	lastActiveAt: timestamp('last_active_at', { withTimezone: true }).notNull().defaultNow()
});

// 关键修正点：将 usersRelations 定义移到这里
export const usersRelations = relations(users, ({ one, many }) => ({
	// 自引用关系：一个用户有一个邀请者
	inviter: one(users, {
		fields: [users.inviterId],
		references: [users.id],
		relationName: 'user_inviter'
	}),
	// 自引用关系：一个用户可以邀请多个用户
	invitees: many(users, {
		relationName: 'user_inviter'
	}),
	sessions: many(sessions),
	keys: many(keys),
	groupMemberships: many(groupMemberships),
	createdGroups: many(groups),
	initiatedMatches: many(matches, { relationName: 'match_actor' }),
	receivedMatches: many(matches, { relationName: 'match_target' }),
	pointTransactions: many(pointTransactions)
}));

// 类型导出
export type User = typeof users.$inferSelect;
export type NewUser = typeof users.$inferInsert;
