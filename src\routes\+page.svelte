<script lang="ts">
	import { onMount } from 'svelte';
	import { goto } from '$app/navigation';
	import { browser } from '$app/environment';
	import { authenticateWithTelegram, isTelegramEnvironment } from '$lib/telegram/init';
	import { Page, Navbar, Block, Button, BlockTitle } from 'konsta/svelte';
	import type { PageData } from './$types';

	let { data }: { data: PageData } = $props();

	let isLoading = false;
	let error = '';
	let isAuthenticated = false;

	onMount(async () => {
		// 如果用户已经登录，重定向到适当的页面
		if (data.user) {
			isAuthenticated = true;
			// 检查是否需要完成onboarding
			const needsOnboarding =
				!data.user.age || !data.user.heightCm || !data.user.weightKg || !data.user.bodyType;
			if (needsOnboarding) {
				goto('/onboarding');
			} else {
				goto('/discover');
			}
			return;
		}

		// 检查是否在Telegram环境中
		if (browser && !isTelegramEnvironment()) {
			error = 'This app can only be used within Telegram';
			return;
		}
	});

	async function handleLogin() {
		if (isLoading) return;

		isLoading = true;
		error = '';

		try {
			const result = await authenticateWithTelegram();

			if (result.success) {
				isAuthenticated = true;
				// 重定向到指定页面
				if (result.redirectTo) {
					goto(result.redirectTo);
				} else {
					goto('/discover');
				}
			} else {
				error = result.error || 'Authentication failed';
			}
		} catch (err) {
			error = err instanceof Error ? err.message : 'An unexpected error occurred';
		} finally {
			isLoading = false;
		}
	}
</script>

<Page>
	<Navbar title="CipherX" />

	<Block>
		<div class="text-center">
			<img src="/CipherXlogo.png" alt="CipherX Logo" class="mx-auto mb-6 h-24 w-24" />
			<BlockTitle>Welcome to CipherX</BlockTitle>
			<p class="mb-6 text-gray-600">
				Your gateway to authentic kink communities. Connect, explore, and discover your tribe in a
				safe and inclusive environment.
			</p>
		</div>
	</Block>

	{#if error}
		<Block>
			<div class="rounded-md bg-red-100 p-4 text-red-700">
				{error}
			</div>
		</Block>
	{/if}

	{#if !isAuthenticated && !error}
		<Block>
			<Button large fill disabled={isLoading} on:click={handleLogin}>
				{#if isLoading}
					<div class="flex items-center">
						<div
							class="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent"
						></div>
						Connecting...
					</div>
				{:else}
					Start Your Journey
				{/if}
			</Button>
		</Block>

		<Block>
			<div class="text-center text-sm text-gray-500">
				<p>By continuing, you agree to our Terms of Service and Privacy Policy.</p>
				<p class="mt-2">This app uses Telegram for secure authentication.</p>
			</div>
		</Block>
	{/if}

	{#if isAuthenticated}
		<Block>
			<div class="text-center">
				<div class="mb-4 text-green-600">✅ Successfully authenticated!</div>
				<p class="text-gray-600">Redirecting you to your dashboard...</p>
			</div>
		</Block>
	{/if}
</Page>
