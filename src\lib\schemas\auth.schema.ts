// src/lib/schemas/auth.schema.ts
// CipherX TMA - 认证相关业务流程验证规则 (最终版)
// 职责：定义所有与认证、登录相关的、类型安全的数据契约。

import { z } from 'zod';
import {
	createIdSchema,
	telegramUserIdSchema,
	telegramUsernameSchema,
	languageCodeSchema
} from './common.schema';

/**
 * 内部 schema，用于验证从 initData 的 user 字段中成功解析出的JSON对象。
 * 它不直接导出，仅供内部 transform 使用。
 */
const telegramUserObjectInInitDataSchema = z.object({
	id: telegramUserIdSchema,
	first_name: z.string(),
	last_name: z.string().optional(),
	username: telegramUsernameSchema.optional(),
	language_code: languageCodeSchema.optional(),
	is_premium: z.boolean().optional()
});

/**
 * 验证从 Telegram Mini App 接收到的完整 initData 字符串。
 * 这是我们认证流程的入口点契约，设计得极其健壮。
 */
export const initDataSchema = z.string().transform((str, ctx) => {
	try {
		const params = new URLSearchParams(str);
		const hash = params.get('hash');
		const userJson = params.get('user');
		const authDate = params.get('auth_date');

		if (!hash || !userJson || !authDate) {
			ctx.addIssue({ code: z.ZodIssueCode.custom, message: 'error_initdata_incomplete' });
			return z.NEVER;
		}

		return {
			raw: str,
			hash: hash,
			auth_date: z.coerce.number().int().positive().parse(authDate),
			user: telegramUserObjectInInitDataSchema.parse(JSON.parse(userJson))
		};
	} catch (e) {
		ctx.addIssue({ code: z.ZodIssueCode.custom, message: 'error_initdata_parse_failed' });
		return z.NEVER;
	}
});

/**
 * 这是我们唯一的登录 API 端点 (/api/auth/telegram) 需要验证的请求体。
 */
export const loginApiSchema = z.object({
	// 它只包含一个字段，就是经过我们上面那个强大的 initDataSchema 验证过的 initData
	initData: initDataSchema
});

/**
 * 用于未来邀请码功能的验证 Schema
 */
export const inviteCodeSchema = z.object({
	code: z.string().length(8, 'error_invite_code_length').toUpperCase(),
	inviterId: createIdSchema('usr_')
});

// --- 类型导出 ---
export type TelegramInitData = z.infer<typeof initDataSchema>;
export type LoginApiData = z.infer<typeof loginApiSchema>;
export type InviteCodeData = z.infer<typeof inviteCodeSchema>;
