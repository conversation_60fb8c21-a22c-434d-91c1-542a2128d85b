// src/lib/telegram/init.ts
// CipherX TMA - Telegram Mini App初始化配置

import { 
	initInitData, 
	initViewport, 
	initThemeParams,
	initMainButton,
	initBackButton,
	initHapticFeedback,
	mockTelegramEnv,
	parseInitData,
	retrieveLaunchParams
} from '@telegram-apps/sdk';
import { dev } from '$app/environment';
import { generateTestInitData } from './mock';

/**
 * 初始化Telegram Mini App SDK
 * 在开发环境中使用mock数据，在生产环境中使用真实的Telegram数据
 */
export async function initTelegramApp() {
	try {
		// 在开发环境中设置mock环境
		if (dev) {
			console.log('🔧 Development mode: Setting up mock Telegram environment');
			setupMockEnvironment();
		}

		// 初始化核心组件
		const [initData] = initInitData();
		const viewport = initViewport();
		const themeParams = initThemeParams();
		const mainButton = initMainButton();
		const backButton = initBackButton();
		const hapticFeedback = initHapticFeedback();

		console.log('✅ Telegram Mini App initialized successfully');
		console.log('📱 Viewport:', viewport.state());
		console.log('🎨 Theme:', themeParams.state());
		console.log('📊 Init Data:', initData.state());

		return {
			initData,
			viewport,
			themeParams,
			mainButton,
			backButton,
			hapticFeedback
		};
	} catch (error) {
		console.error('❌ Failed to initialize Telegram Mini App:', error);
		
		// 在开发环境中，即使初始化失败也要继续
		if (dev) {
			console.log('🔧 Development mode: Continuing with mock setup');
			setupMockEnvironment();
			return null;
		}
		
		throw error;
	}
}

/**
 * 设置开发环境的mock数据
 */
function setupMockEnvironment() {
	const mockInitDataRaw = generateTestInitData.basic();
	
	try {
		mockTelegramEnv({
			themeParams: {
				accentTextColor: '#6ab2f2',
				bgColor: '#17212b',
				buttonColor: '#5288c1',
				buttonTextColor: '#ffffff',
				destructiveTextColor: '#ec3942',
				headerBgColor: '#17212b',
				hintColor: '#708499',
				linkColor: '#6ab3f3',
				secondaryBgColor: '#232e3c',
				sectionBgColor: '#17212b',
				sectionHeaderTextColor: '#6ab3f3',
				subtitleTextColor: '#708499',
				textColor: '#f5f5f5',
			},
			initData: parseInitData(mockInitDataRaw),
			initDataRaw: mockInitDataRaw,
			version: '7.2',
			platform: 'tdesktop',
		});
		
		console.log('🎭 Mock Telegram environment set up');
		console.log('📝 Mock initData:', mockInitDataRaw);
	} catch (error) {
		console.error('❌ Failed to setup mock environment:', error);
	}
}

/**
 * 获取当前的initData，优先使用真实数据，开发环境fallback到mock数据
 */
export function getInitData(): string | null {
	try {
		if (!dev) {
			// 生产环境：尝试获取真实的initData
			const { initDataRaw } = retrieveLaunchParams();
			return initDataRaw || null;
		} else {
			// 开发环境：使用mock数据
			return generateTestInitData.basic();
		}
	} catch (error) {
		console.error('Failed to get initData:', error);
		
		// 开发环境fallback
		if (dev) {
			return generateTestInitData.basic();
		}
		
		return null;
	}
}

/**
 * 检查当前是否在Telegram环境中运行
 */
export function isTelegramEnvironment(): boolean {
	if (dev) {
		return true; // 开发环境总是返回true
	}
	
	try {
		const { initDataRaw } = retrieveLaunchParams();
		return !!initDataRaw;
	} catch {
		return false;
	}
}

/**
 * 发送认证请求到后端
 */
export async function authenticateWithTelegram(): Promise<{
	success: boolean;
	redirectTo?: string;
	error?: string;
}> {
	try {
		const initDataRaw = getInitData();
		
		if (!initDataRaw) {
			throw new Error('No initData available');
		}

		const response = await fetch('/api/auth/telegram', {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
			},
			body: JSON.stringify({
				initData: {
					raw: initDataRaw,
					// 解析initData以获取其他字段
					...parseInitDataForAuth(initDataRaw)
				}
			}),
		});

		if (!response.ok) {
			const errorData = await response.json().catch(() => ({}));
			throw new Error(errorData.message || 'Authentication failed');
		}

		const result = await response.json();
		return result;
	} catch (error) {
		console.error('Authentication error:', error);
		return {
			success: false,
			error: error instanceof Error ? error.message : 'Unknown error'
		};
	}
}

/**
 * 解析initData用于认证
 */
function parseInitDataForAuth(initDataRaw: string) {
	try {
		const params = new URLSearchParams(initDataRaw);
		const userJson = params.get('user');
		const authDate = params.get('auth_date');
		const hash = params.get('hash');

		if (!userJson || !authDate || !hash) {
			throw new Error('Invalid initData format');
		}

		return {
			user: JSON.parse(userJson),
			auth_date: parseInt(authDate),
			hash
		};
	} catch (error) {
		console.error('Failed to parse initData:', error);
		throw error;
	}
}
