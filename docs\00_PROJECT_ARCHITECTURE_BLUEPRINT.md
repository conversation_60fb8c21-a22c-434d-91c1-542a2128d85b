CipherX TMA: 项目架构蓝图 (Project Architecture Blueprint)
版本 (Version): 0.1.0 (奠基版)

最后更新 (Last Updated): 2025-06-28
首席技术官 (CTO): Gemini
项目总负责人 (CEO): 您

前言
本文件是 CipherX TMA 项目的唯一事实来源 (Single Source of Truth)，旨在为所有人类开发者与 AI Copilot 提供一个关于项目结构、技术选型、设计哲学和开发流程的共同理解基准。在进行任何新的开发任务前，都应首先阅读并遵循本蓝图的指导。

第一章：核心理念与技术栈
1.1 架构哲学
服务器是唯一事实来源： 所有核心数据和业务逻辑的权威版本都存在于后端。

模块化单体： 在单一的 SvelteKit 应用中，通过清晰的服务和模块划分，实现“高内聚、低耦合”，兼顾开发效率与未来扩展性。

渐进式引导与价值驱动： 以“零门槛”吸引用户加入社区，通过社区价值和功能解锁来激励用户完善资料和深度参与。

代码即架构： 使用 Drizzle Schema 定义数据库结构，使用配置文件定义项目行为，所有基础设施都应纳入版本控制。

安全第一： 遵循 HttpOnly Cookie、initData 双重验证、最小权限原则等最佳实践，构建可靠的安全体系。

1.2 核心技术栈
全栈框架: SvelteKit

UI 方案: Konsta UI + Tailwind CSS

认证与会话: Lucia Auth (基于TMA initData)

数据库与ORM: PostgreSQL + Drizzle ORM

表单与验证: SvelteKit Superforms + Zod

国际化 (i18n): ParaglideJS

Telegram Bot 框架: grammY

动效与视觉: LottieFiles, GSAP

核心开发工具: pnpm, TypeScript, ESLINT/prettier, tsx, Vite
第二章：完整项目目录结构
这是项目的“城市规划总图”，规定了每一类资产的存放位置。

/BLUEX/ (项目根目录,项目已经改名叫CipherX)
├── .env # ‼️ 机密！存放BOT_TOKEN, DATABASE_URL等。必须在.gitignore中。
├── .gitignore # Git 忽略文件配置。
├── biome.json # Biome (或ESLint/Prettier) 的配置文件。
├── drizzle.config.ts # Drizzle Kit 迁移工具的配置文件。
├── package.json # 项目依赖与脚本的“户口本”。
├── pnpm-lock.yaml # 锁定依赖精确版本，保证环境一致性。
├── postcss.config.js # PostCSS 配置文件。
├── svelte.config.js # SvelteKit 核心配置文件 (适配器, 别名)。
├── tailwind.config.ts # Tailwind CSS 配置文件。
├── tsconfig.json # TypeScript 编译器配置。
├── vite.config.ts # Vite 构建工具配置文件。
├── messages/             # paraglidejs i18n相关 存放所有面向用户的、支持多语言的“UI静态文本”源文件。
│   │   ├── en.json
│   │   └── zh.json
├── /docs/ # 存放所有项目文档的“档案馆”。
│ ├── 00_PROJECT_ARCHITECTURE_BLUEPRINT.md # 👈 本文件。
│ └── 01_PROGRESS_LOG.md # AI 协同的“航行日志”。
│
├── /scripts/ # 存放一次性或辅助性的后端脚本 (如数据库填充, 群组初始化工具)。
│ └── init-group.ts
│
├── /src/ # 核心源代码目录。
│ ├── app.html # 应用的全局 HTML “躯壳”。
│ ├── app.css # 应用的全局 CSS “皮肤”。
│ ├── app.d.ts # SvelteKit 全局类型声明 (如 App.Locals)。
│ ├── hooks.server.ts # 全局服务器端钩子 (“总门卫”，处理会话验证)。
│ │
│ ├── lib/ # 应用的“共享大脑”和“工具箱”。
│ │ ├── components/ # 全局、可复用的 UI 组件。
│ │ ├── constants/ # 全局常量 (如 KINK_ROLES 定义, 积分规则)。
│ │ ├── paraglide/ # Paraglide 自动生成的 i18n 文件 (由工具管理)。
│ │ ├── schemas/ # Zod 验证 Schemas (业务流程的数据契约)。
│ │ ├── server/ # 严格的“仅后端”代码。
│ │ │ ├── auth.ts # - Lucia 认证实例。
│ │ │ ├── bot/ # - grammY Bot 核心逻辑。
│ │ │ ├── db/ # - Drizzle ORM 相关。
│ │ │ └── services/ # - 可复用的后端业务逻辑层。
│ │ ├── stores/ # 全局、客户端 UI 状态管理 (ui.store.ts)。
│ │ ├── telegram/ # 封装与 TMA SDK 交互的逻辑。
│ │ ├── types/ # 全局共享的、通用的 TypeScript 类型。
│ │ └── utils/ # 全局共享的、通用的工具函数。
│ │
│ └── routes/ # 所有页面和 API 路由。
│ ├── +layout.server.ts # - 根布局数据加载 (全局用户状态)。
│ ├── +layout.svelte # - 根布局 UI (应用外壳)。
│ ├── +page.svelte # - 应用首页 (Landing Page)。
│ │
│ ├── api/ # 纯数据 API 端点。
│ │
│ ├── (app)/ # [路由组] 需要登录才能访问的核心应用页面。
│ │ ├── +layout.svelte # - (可选) app 组的专属布局 (如带底部导航栏)。
│ │ ├── discover/
│ │ └── ...
│ │
│ └── (public)/ # [路由组] 公开的或使用不同布局的页面。
│ ├── onboarding/
│ └── map/[code]/
│
└── /static/ # 存放无需编译的静态文件 (favicon, tonconnect-manifest.json)。
第三章：核心模块代码放置原则
UI 组件:

全局共享组件 (如 Button, Card) -> src/lib/components/

仅特定页面使用的组件 (如 ProfileSettingsForm) -> 与该页面的 +page.svelte 放在同一目录下。

服务器端逻辑:

请求入口 (Controller): 放在 src/routes/.../+page.server.ts (load, actions) 或 +server.ts 中。保持精简。

可复用业务逻辑 (Service): 抽离到 src/lib/server/services/ 中。

认证中间件: 放在 src/hooks.server.ts 中，具体实现可抽离到 src/lib/hooks/。

机器人交互逻辑: 封装在 src/lib/server/bot/ 中。

状态管理:

服务器状态 (Server State): 永远通过 load 函数获取，并通过 data prop 传递。这是唯一事实来源。

客户端UI状态 (Client UI State): 使用 src/lib/stores/ 中的 Svelte Stores 管理 (如模态框开关状态)。

第四章：AI 协同工作流 (BPRA 模型)
为最大化开发效率和准确性，我们与 AI Copilot 的协作遵循 “蓝图-提案-审核-执行 (Blueprint, Proposal, Review, Act)” 模型。

阶段一：蓝图学习 (Blueprint Study) - AI

AI 在开始任务前，必须阅读本文件、PROGRESS_LOG.md 和对应的里程碑指南，以建立完整的上下文。

阶段二：任务阐述 (Task Briefing) - 您

您向 AI 下达一个聚焦于单个模块的、清晰的任务指令。

阶段三：AI 生成“施工提案” (Proposal Generation) - AI

AI 首先输出一份行动计划，列出它将要创建/修改的文件列表，以及每个文件中的核心逻辑变更概要。

阶段四：人机联合审核 (Joint Review) - 我们

您和我一同审查这份“提案”，确保它在业务和技术上都完全正确。

阶段五：授权执行 (Authorize & Act) - 您 & AI

审核通过后，您下达“开始执行”的指令，AI 将严格按照提案生成最终代码。

补充说明 
项目 “CipherX”：架构与战略总纲 (v1.1)
文档目的： 本文件是我们所有讨论的最终沉淀，是一份高度浓缩的“项目创世蓝图”。它将作为您、我、以及您的 AI Copilot 在未来所有开发工作中，共享的、唯一的“核心记忆”和“行动宪章”。

第一章：产品战略与商业模式
核心定位： 一个基于 Telegram Mini App (TMA) 的社区即服务 (CaaS) 与 SocialFi 平台。

目标用户： 全球范围内的、对 Kink 等多元化生活方式感兴趣的、追求高质量和私密社交的成年用户。

核心价值主张 (UVP): 我们不提供一个简单的“左滑右滑”匹配工具，而是提供一个发现和进入经过筛选的、高质量的、有主题的私密社群的独家入口。

增长飞轮 (Growth Flywheel):

吸引 (Attract): 通过一个公开的 Telegram 官方频道和一个免费、可病毒式传播的**“Kink Map 生成器”**工具，将公域流量引导至TMA。

激活 (Activate): 提供零门槛入群体验。用户完成最基础的资料填写后，即可用初始积分加入限定数量的群组，快速体验社区价值。

留存 (Retain): 高质量、经过审核的、无广告骚扰的群组和 Topics 内的深度讨论，形成强大的用户粘性与归属感。

转化与变现 (Convert & Monetize): 当用户在大型群组中产生“精准发现”的需求时，引导其完善高级资料，以解锁V2.0 的高级匹配功能。并通过“积分-代币”经济系统和VIP服务实现商业价值。

运营模式：

群组策略: MVP 阶段采用“国家-取向”大类群组，内部通过“省份/州”和“Kink角色”作为 Topics 进行精细划分。

内容审核: 采用“限制媒体类型 + 机器人初筛 + 用户举报 + 人工管理员终审”的多层次、主动式内容治理模式，确保社区安全。

第二章：核心技术栈
全栈框架: SvelteKit

UI 方案: Konsta UI (移动原生体验) + Tailwind CSS (样式)

认证与会话: Lucia Auth (服务端 Session-Cookie 模型)

数据库与 ORM: PostgreSQL + Drizzle ORM

表单与验证: SvelteKit Superforms + Zod

国际化 (i18n): ParaglideJS

Telegram 集成: @telegram-apps/sdk (前端) + grammY (后端Bot)

核心开发工具: pnpm, TypeScript, Vitest, tsx, Biome/Oxlint/Prettier

第三章：核心技术架构
总体架构: 模块化单体 (Modular Monolith)。所有代码在一个仓库中，但通过 src/lib/server/services 等目录结构，在逻辑上实现清晰的“关注点分离”。

认证流程:

入口: 唯一的信任来源是 TMA 的 initData。

验证: 由 SvelteKit 后端 API 端点，使用存储在环境变量中的 BOT_TOKEN 进行加密校验。

会话: 验证成功后，由 Lucia 在数据库 sessions 表中创建记录，并通过一个安全的 HttpOnly Cookie 将 Session ID 返回给客户端。

全局鉴权: hooks.server.ts 拦截所有后续请求，通过 Cookie 验证会话，并将用户信息注入 event.locals。

双重验证: 对于支付等高风险操作，将在验证 Session Cookie 的基础上，要求前端额外提交一份新鲜的 initData 进行二次确认。

数据流:

load 函数: 负责所有数据读取。根 +layout.server.ts 负责加载全局用户状态，各页面的 +page.server.ts 负责加载该页面专属的、详细的数据。

actions 函数: 负责所有数据写入（增、删、改），通过表单提交触发。

缓存策略: 对于不常变的、公开的数据（如 Kink 定义），在 load 函数中采用服务端内存缓存或 Redis 缓存来降低数据库负载。

数据契约：

Drizzle Schema (db/schema/): 数据库物理结构的“代码化蓝图”。

Zod Schema (schemas/): 应用业务逻辑的“数据安检门”。

constants/ & messages/: 分别作为程序化常量和UI文本翻译的“唯一事实来源”。

第四章：开发与协作流程
版本控制 (Git):

严格遵循功能分支工作流 (Feature Branch Workflow)。

main 分支永远保持稳定可部署。所有开发都在 feature/... 分支上进行，通过 Pull Request (PR) 进行代码审查后，再合并到主干。

AI 协同 (BPRA 模型):

蓝图 (Blueprint): AI 首先学习我们共同制定的项目总纲和里程碑文档。

提案 (Proposal): 开发者下达任务指令后，AI 首先输出一份“施工提案”（将要修改/创建的文件列表和核心逻辑概要）。

审核 (Review): 开发者（您）与我（CTO AI）共同审查这份提案的合理性。

执行 (Act): 审核通过后，AI 才开始生成最终的、完整的代码。

项目进度管理:

/docs/01_PROGRESS_LOG.md 是我们共享的“航行日志”，用于实时追踪每个任务的完成状态，以对抗 AI 的上下文窗口限制。