项目 “CipherX”：架构与战略总纲 (v1.0)
文档目的： 本文件是我们所有讨论的最终沉淀，是一份高度浓缩的“项目创世蓝图”。它将作为您、我、以及您的 AI Copilot 在未来所有开发工作中，共享的、唯一的“核心记忆”和“行动宪章”。

第一章：产品战略与商业模式
核心定位： 一个基于 Telegram Mini App (TMA) 的社区即服务 (CaaS) 与 SocialFi 平台。

目标用户： 全球范围内的、对 Kink 等多元化生活方式感兴趣的、追求高质量和私密社交的成年用户。

核心价值主张 (UVP): 我们不提供一个简单的“左滑右滑”匹配工具，而是提供一个发现和进入经过筛选的、高质量的、有主题的私密社群的独家入口。

增长飞轮 (Growth Flywheel):

吸引 (Attract): 通过一个公开的 Telegram 官方频道和一个免费、可病毒式传播的**“Kink Map 生成器”**工具，将公域流量引导至TMA。

激活 (Activate): 提供零门槛入群体验。用户完成最基础的资料填写后，即可用初始积分加入限定数量的群组，快速体验社区价值。

留存 (Retain): 高质量、经过审核的、无广告骚扰的群组和 Topics 内的深度讨论，形成强大的用户粘性与归属感。

转化与变现 (Convert & Monetize): 当用户在大型群组中产生“精准发现”的需求时，引导其完善高级资料，以解锁V2.0 的高级匹配功能。并通过“积分-代币”经济系统和VIP服务实现商业价值。

运营模式：

群组策略: MVP 阶段采用“国家-取向”大类群组，内部通过“省份/州”和“Kink角色”作为 Topics 进行精细划分。

内容审核: 采用“限制媒体类型 + 机器人初筛 + 用户举报 + 人工管理员终审”的多层次、主动式内容治理模式，确保社区安全。

第二章：核心技术栈
全栈框架: SvelteKit

UI 方案: Konsta UI (移动原生体验) + Tailwind CSS (样式)

认证与会话: Lucia Auth (服务端 Session-Cookie 模型)

数据库与 ORM: PostgreSQL + Drizzle ORM

表单与验证: SvelteKit Superforms + Zod

国际化 (i18n): ParaglideJS

Telegram 集成: @telegram-apps/sdk (前端) + grammY (后端Bot)

核心开发工具: pnpm, TypeScript, Vitest, tsx, Biome/Oxlint/Prettier

第三章：核心技术架构
总体架构: 模块化单体 (Modular Monolith)。所有代码在一个仓库中，但通过 src/lib/server/services 等目录结构，在逻辑上实现清晰的“关注点分离”。

认证流程:

入口: 唯一的信任来源是 TMA 的 initData。

验证: 由 SvelteKit 后端 API 端点，使用存储在环境变量中的 BOT_TOKEN 进行加密校验。

会话: 验证成功后，由 Lucia 在数据库 sessions 表中创建记录，并通过一个安全的 HttpOnly Cookie 将 Session ID 返回给客户端。

全局鉴权: hooks.server.ts 拦截所有后续请求，通过 Cookie 验证会话，并将用户信息注入 event.locals。

双重验证: 对于支付等高风险操作，将在验证 Session Cookie 的基础上，要求前端额外提交一份新鲜的 initData 进行二次确认。

数据流:

load 函数: 负责所有数据读取。根 +layout.server.ts 负责加载全局用户状态，各页面的 +page.server.ts 负责加载该页面专属的、详细的数据。

actions 函数: 负责所有数据写入（增、删、改），通过表单提交触发。

缓存策略: 对于不常变的、公开的数据（如 Kink 定义），在 load 函数中采用服务端内存缓存或 Redis 缓存来降低数据库负载。

数据契约：

Drizzle Schema (db/schema/): 数据库物理结构的“代码化蓝图”。

Zod Schema (schemas/): 应用业务逻辑的“数据安检门”。

constants/ & messages/: 分别作为程序化常量和UI文本翻译的“唯一事实来源”。

第四章：开发与协作流程
版本控制 (Git):

严格遵循功能分支工作流 (Feature Branch Workflow)。

main 分支永远保持稳定可部署。所有开发都在 feature/... 分支上进行，通过 Pull Request (PR) 进行代码审查后，再合并到主干。

AI 协同 (BPRA 模型):

蓝图 (Blueprint): AI 首先学习我们共同制定的项目总纲和里程碑文档。

提案 (Proposal): 开发者下达任务指令后，AI 首先输出一份“施工提案”（将要修改/创建的文件列表和核心逻辑概要）。

审核 (Review): 开发者（您）与我（CTO AI）共同审查这份提案的合理性。

执行 (Act): 审核通过后，AI 才开始生成最终的、完整的代码。

项目进度管理:

/docs/01_PROGRESS_LOG.md 是我们共享的“航行日志”，用于实时追踪每个任务的完成状态，以对抗 AI 的上下文窗口限制。