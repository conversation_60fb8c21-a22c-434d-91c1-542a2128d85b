// src/routes/api/auth/telegram/+server.ts
// CipherX TMA - Telegram认证API端点

import { json, error } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { auth } from '$lib/server/auth';
import { db } from '$lib/server/db';
import { users } from '$lib/server/db/schema';
import { loginApiSchema } from '$lib/schemas/auth.schema';
import { validateInitDataSignature } from '$lib/telegram/mock';
import { nanoid } from 'nanoid';
import { eq } from 'drizzle-orm';
import { dev } from '$app/environment';

/**
 * POST /api/auth/telegram
 * 处理Telegram Mini App的登录请求
 */
export const POST: RequestHandler = async ({ request, cookies }) => {
	try {
		// 解析请求体
		const body = await request.json();
		
		// 验证请求数据格式
		const parseResult = loginApiSchema.safeParse(body);
		if (!parseResult.success) {
			console.error('Invalid request format:', parseResult.error);
			throw error(400, { message: 'Invalid request format' });
		}

		const { initData } = parseResult.data;
		const botToken = process.env.BOT_TOKEN;

		if (!botToken) {
			console.error('BOT_TOKEN not configured');
			throw error(500, { message: 'Server configuration error' });
		}

		// 验证initData签名
		if (!dev && !validateInitDataSignature(initData.raw, botToken)) {
			console.error('Invalid initData signature');
			throw error(401, { message: 'Invalid authentication data' });
		}

		// 检查auth_date是否过期（24小时）
		const authDate = new Date(initData.auth_date * 1000);
		const now = new Date();
		const hoursDiff = (now.getTime() - authDate.getTime()) / (1000 * 60 * 60);
		
		if (!dev && hoursDiff > 24) {
			console.error('InitData expired:', { authDate, now, hoursDiff });
			throw error(401, { message: 'Authentication data expired' });
		}

		const telegramUser = initData.user;
		
		// 查找现有用户
		let existingUser = await db.query.users.findFirst({
			where: eq(users.telegramUserId, telegramUser.id)
		});

		let userId: string;

		if (existingUser) {
			// 更新现有用户的最后活跃时间和可能的用户名变化
			userId = existingUser.id;
			await db
				.update(users)
				.set({
					telegramUsername: telegramUser.username || null,
					lastActiveAt: new Date(),
					updatedAt: new Date()
				})
				.where(eq(users.id, userId));
		} else {
			// 创建新用户
			userId = `usr_${nanoid()}`;
			const kinkMapCode = generateKinkMapCode();

			await db.insert(users).values({
				id: userId,
				telegramUserId: telegramUser.id,
				nickname: telegramUser.first_name,
				telegramUsername: telegramUser.username || null,
				languageCode: telegramUser.language_code || 'en',
				kinkMapCode,
				createdAt: new Date(),
				updatedAt: new Date(),
				lastActiveAt: new Date()
			});
		}

		// 创建会话
		const session = await auth.createSession(userId, {});
		const sessionCookie = auth.createSessionCookie(session.id);

		// 设置会话cookie
		cookies.set(sessionCookie.name, sessionCookie.value, {
			path: '.',
			...sessionCookie.attributes
		});

		// 获取完整的用户信息
		const fullUser = await db.query.users.findFirst({
			where: eq(users.id, userId)
		});

		if (!fullUser) {
			throw error(500, { message: 'Failed to retrieve user data' });
		}

		// 检查用户是否需要完成onboarding
		const needsOnboarding = !fullUser.age || !fullUser.heightCm || !fullUser.weightKg || !fullUser.bodyType;

		return json({
			success: true,
			user: {
				id: fullUser.id,
				nickname: fullUser.nickname,
				kinkMapCode: fullUser.kinkMapCode,
				needsOnboarding
			},
			redirectTo: needsOnboarding ? '/onboarding' : '/discover'
		});

	} catch (err) {
		console.error('Login error:', err);
		
		// 如果是我们抛出的error，直接返回
		if (err && typeof err === 'object' && 'status' in err) {
			throw err;
		}
		
		// 其他错误返回500
		throw error(500, { message: 'Internal server error' });
	}
};

/**
 * 生成唯一的Kink Map代码
 */
function generateKinkMapCode(): string {
	// 生成8位随机字符串，包含数字和大写字母
	const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
	let result = '';
	for (let i = 0; i < 8; i++) {
		result += chars.charAt(Math.floor(Math.random() * chars.length));
	}
	return result;
}
