// src/routes/(public)/onboarding/+page.server.ts
import { fail, redirect } from '@sveltejs/kit';
import { superValidate } from 'sveltekit-superforms';
import { zod } from 'sveltekit-superforms/adapters';
import { basicProfileSchema } from '$lib/schemas/profile.schema';
import { getFullUserProfile, updateUserProfile } from '$lib/server/services/user.service';
import type { Actions, PageServerLoad } from './$types';

import { bodyTypeEnum } from '$lib/server/db/schema';

export const load: PageServerLoad = async (event) => {
	// Redirect to home if the user is not logged in
	if (!event.locals.user) {
		throw redirect(303, '/');
	}

    // Fetch the user's current profile data to pre-fill the form
    const userProfile = await getFullUserProfile(event.locals.user);

	// Initialize the form with the user's existing data
	const form = await superValidate(userProfile, zod(basicProfileSchema));

	return { form, bodyTypes: bodyTypeEnum.enumValues };
};

export const actions: Actions = {
	default: async (event) => {
		// Redirect to home if the user is not logged in
		if (!event.locals.user) {
			throw redirect(303, '/');
		}

		const form = await superValidate(event, zod(basicProfileSchema));

		if (!form.valid) {
			// If the form is not valid, return a failure with the form data.
			// Superforms will automatically display the errors on the client.
			return fail(400, { form });
		}

		// Update the user's profile in the database
		const { success, error } = await updateUserProfile(event.locals.user.id, form.data);

		if (!success) {
			// Handle potential database errors
            // You might want to return a specific error message to the form
			return fail(500, { form });
		}

		// On success, redirect the user to their dashboard or next step
		throw redirect(303, '/discover');
	}
};
