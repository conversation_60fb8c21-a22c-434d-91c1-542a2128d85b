// src/lib/telegram/mock.ts
// CipherX TMA - Telegram initData Mock工具
// 用于开发和测试环境生成有效的Telegram initData

import { createHmac } from 'crypto';
import { nanoid } from 'nanoid';

export interface MockTelegramUser {
	id: number;
	first_name: string;
	last_name?: string;
	username?: string;
	language_code?: string;
	is_premium?: boolean;
	allows_write_to_pm?: boolean;
	photo_url?: string;
}

export interface MockInitDataOptions {
	user?: Partial<MockTelegramUser>;
	query_id?: string;
	chat_type?: 'sender' | 'private' | 'group' | 'supergroup' | 'channel';
	chat_instance?: string;
	start_param?: string;
	auth_date?: number;
	botToken?: string;
}

/**
 * 生成用于开发和测试的Telegram initData
 * 包含正确的HMAC-SHA256签名验证
 */
export function generateMockInitData(options: MockInitDataOptions = {}): string {
	const {
		user = {},
		query_id = nanoid(),
		chat_type = 'private',
		chat_instance = Math.random().toString(),
		start_param,
		auth_date = Math.floor(Date.now() / 1000),
		botToken = process.env.BOT_TOKEN || '5768337691:AAH5YkoiEuPk8-FZa32hStHTqXiLPtAEhx8'
	} = options;

	// 默认用户数据
	const defaultUser: MockTelegramUser = {
		id: 279058397,
		first_name: 'Test',
		last_name: 'User',
		username: 'testuser',
		language_code: 'en',
		is_premium: false,
		allows_write_to_pm: true,
		...user
	};

	// 构建initData参数
	const params: Record<string, string> = {
		query_id,
		user: JSON.stringify(defaultUser),
		auth_date: auth_date.toString(),
		chat_type,
		chat_instance
	};

	if (start_param) {
		params.start_param = start_param;
	}

	// 生成签名
	const hash = generateInitDataHash(params, botToken);
	params.hash = hash;

	// 转换为URL参数格式
	const searchParams = new URLSearchParams();
	Object.entries(params).forEach(([key, value]) => {
		searchParams.append(key, value);
	});

	return searchParams.toString();
}

/**
 * 根据Telegram官方算法生成initData的HMAC-SHA256签名
 */
function generateInitDataHash(params: Record<string, string>, botToken: string): string {
	// 1. 排除hash参数，按字母顺序排序
	const sortedParams = Object.entries(params)
		.filter(([key]) => key !== 'hash')
		.sort(([a], [b]) => a.localeCompare(b))
		.map(([key, value]) => `${key}=${value}`);

	// 2. 用换行符连接
	const dataCheckString = sortedParams.join('\n');

	// 3. 使用"WebAppData"作为key，对bot token进行HMAC-SHA256
	const secretKey = createHmac('sha256', 'WebAppData').update(botToken).digest();

	// 4. 使用上一步的结果作为key，对数据字符串进行HMAC-SHA256
	const hash = createHmac('sha256', secretKey).update(dataCheckString).digest('hex');

	return hash;
}

/**
 * 验证initData的签名是否正确
 */
export function validateInitDataSignature(initData: string, botToken: string): boolean {
	try {
		const params = new URLSearchParams(initData);
		const receivedHash = params.get('hash');
		
		if (!receivedHash) {
			return false;
		}

		// 重新构建参数对象
		const paramsObj: Record<string, string> = {};
		params.forEach((value, key) => {
			paramsObj[key] = value;
		});

		// 生成期望的hash
		const expectedHash = generateInitDataHash(paramsObj, botToken);
		
		return receivedHash === expectedHash;
	} catch (error) {
		console.error('Error validating initData signature:', error);
		return false;
	}
}

/**
 * 预定义的测试用户数据
 */
export const MOCK_USERS = {
	testUser: {
		id: 279058397,
		first_name: 'Test',
		last_name: 'User',
		username: 'testuser',
		language_code: 'en',
		is_premium: false
	},
	premiumUser: {
		id: 123456789,
		first_name: 'Premium',
		last_name: 'User',
		username: 'premiumuser',
		language_code: 'en',
		is_premium: true
	},
	chineseUser: {
		id: 987654321,
		first_name: '测试',
		last_name: '用户',
		username: 'chineseuser',
		language_code: 'zh',
		is_premium: false
	}
} as const;

/**
 * 快速生成常用的测试initData
 */
export const generateTestInitData = {
	basic: () => generateMockInitData({ user: MOCK_USERS.testUser }),
	premium: () => generateMockInitData({ user: MOCK_USERS.premiumUser }),
	chinese: () => generateMockInitData({ user: MOCK_USERS.chineseUser }),
	withStartParam: (startParam: string) => 
		generateMockInitData({ user: MOCK_USERS.testUser, start_param: startParam }),
	expired: () => generateMockInitData({ 
		user: MOCK_USERS.testUser, 
		auth_date: Math.floor(Date.now() / 1000) - 86400 * 2 // 2天前
	})
};
