/**
 * CipherX 积分系统常量定义
 */

// 1. 定义所有合法的、程序可用的交易类型 Key
export const POINT_TRANSACTION_TYPES = {
	// 赚取积分
	REGISTRATION_BONUS: 'registration_bonus',
	PROFILE_COMPLETION_BONUS: 'profile_completion_bonus',
	DAILY_CHECK_IN: 'daily_check_in',
	INVITE_SUCCESS: 'invite_success',
	CONTENT_CONTRIBUTION: 'content_contribution', // 未来可用于奖励优质内容

	// 消耗积分
	GROUP_SUBSCRIPTION_FEE: 'group_subscription_fee',
	ADVANCED_SEARCH: 'advanced_search_cost', // 未来高级搜索可能消耗积分
	SUPER_LIKE: 'super_like_cost',

	// 其他
	ADMIN_ADJUSTMENT: 'admin_adjustment' // 管理员手动调整
} as const;

export type PointTransactionType =
	(typeof POINT_TRANSACTION_TYPES)[keyof typeof POINT_TRANSACTION_TYPES];

// 2. 定义不同行为对应的标准积分值
export const POINTS = {
	FOR_PROFILE_COMPLETION: 500, // 假设完成基础资料奖励500分
	FOR_DAILY_CHECK_IN: 10,
	FOR_INVITE_SUCCESS: 100,
	WEEKLY_GROUP_FEE: -10 // 消耗我们用负数表示
};
