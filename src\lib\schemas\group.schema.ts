// src/lib/schemas/group.schema.ts
// CipherX TMA - 群组相关验证规则

import { z } from 'zod';
import {
	createIdSchema,
	countryCodeSchema,
	provinceCodeSchema,
	telegramUserIdSchema
} from './common.schema.js';

// 导入“事实来源”
import { GROUP_THEMES } from '$lib/constants/community';
import { groupRoleEnum } from '$lib/server/db/schema/_enums';
/**
 * 创建群组验证 Schema (最终修正版)
 */
export const createGroupSchema = z.object({
	id: createIdSchema('grp_'),
	slug: z
		.string()
		.min(3, 'error_slug_too_short')
		.max(100, 'error_slug_too_long')
		.regex(/^[a-z0-9-]+$/, 'error_slug_format')
		.refine((slug) => !slug.startsWith('-') && !slug.endsWith('-'), 'error_slug_no_hyphen_ends'),

	telegramChatId: z.number().int(),
	nameEn: z.string(),
	nameZh: z.string().optional(),
	descriptionEn: z.string().min(10).max(500).optional(),
	descriptionZh: z.string().min(10).max(500).optional(),

	countryCode: countryCodeSchema,
	provinceCode: provinceCodeSchema.optional().nullable(),

	theme: z.enum(Object.values(GROUP_THEMES) as [string, ...string[]]),

	creatorId: createIdSchema('usr_') // 验证创建者必须是用户ID
});

/**
 * 更新群组信息时，用于表单验证的 Schema
 */
export const updateGroupSchema = createGroupSchema
	.pick({
		descriptionEn: true,
		descriptionZh: true,
		nameEn: true, // 允许修改显示名称
		nameZh: true
	})
	.partial(); // 所有字段都变为可选

// /**
//  * 群组成员操作验证 Schema
//  */
// export const groupMembershipSchema = z.object({
//   userId: createIdSchema('usr_'),
//   groupId: createIdSchema('usr_'),
//   role: z.enum(['member', 'moderator', 'admin']).default('member'),
//   subscriptionExpiresAt: z.date(),
// });

/**
 * 加入群组操作的验证 Schema
 */
export const joinGroupSchema = z.object({
	groupId: createIdSchema('grp_')
	// userId 将从 session 中获取，而不是由客户端提交，因此这里不需要
});

/**
 * 群组搜索过滤器验证 Schema
 */
export const groupSearchFiltersSchema = z.object({
	countryCode: countryCodeSchema.optional(),
	provinceCode: provinceCodeSchema.optional(),
	theme: z.enum(Object.values(GROUP_THEMES) as [string, ...string[]]).optional(),
	memberCountRange: z.tuple([z.number().int().min(0), z.number().int().max(10000)]).optional(),
	createdAfter: z.date().optional(),
	createdBefore: z.date().optional()
});

/**
 * 群组统计验证 Schema
 */
export const groupStatsSchema = z.object({
	groupId: createIdSchema('usr_'),
	dateRange: z
		.object({
			start: z.date(),
			end: z.date()
		})
		.optional()
});

/**
 * 更新群组成员角色的验证 Schema
 */
export const updateGroupRoleSchema = z.object({
	userId: createIdSchema('usr_'),
	groupId: createIdSchema('grp_'),
	newRole: z.enum(groupRoleEnum.enumValues) // ✅ 从数据库枚举导入
});

/**
 * 群组邀请验证 Schema (最终修正版)
 */
export const groupInviteSchema = z
	.object({
		groupId: createIdSchema('grp_'), // ✅ 修正：验证群组ID，而不是用户ID
		inviterId: createIdSchema('usr_'),
		inviteeId: createIdSchema('usr_').optional(),
		inviteeTelegramUserId: telegramUserIdSchema.optional(),
		message: z.string().max(200).optional()
	})
	.refine((data) => data.inviteeId || data.inviteeTelegramUserId, {
		message: 'error_invitee_identifier_required' // ✅ 修正：使用 i18n Key
	});

/**
 * 群组设置验证 Schema
 */
export const groupSettingsSchema = z.object({
	groupId: createIdSchema('usr_'),
	settings: z.object({
		isPublic: z.boolean().default(true),
		requireApproval: z.boolean().default(false),
		maxMembers: z.number().int().min(10).max(10000).default(1000),
		subscriptionFee: z.number().int().min(0).default(0),
		allowInvites: z.boolean().default(true),
		moderationLevel: z.enum(['low', 'medium', 'high']).default('medium')
	})
});

// 导出类型定义
export type CreateGroupData = z.infer<typeof createGroupSchema>;
export type UpdateGroupData = z.infer<typeof updateGroupSchema>;
// export type GroupMembershipData = z.infer<typeof groupMembershipSchema>;
export type JoinGroupData = z.infer<typeof joinGroupSchema>;
export type GroupSearchFilters = z.infer<typeof groupSearchFiltersSchema>;
export type GroupStatsData = z.infer<typeof groupStatsSchema>;
export type UpdateGroupRoleData = z.infer<typeof updateGroupRoleSchema>;
export type GroupInviteData = z.infer<typeof groupInviteSchema>;
export type GroupSettingsData = z.infer<typeof groupSettingsSchema>;
