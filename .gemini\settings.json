{"mcpServers": {"Playwright": {"command": "npx", "args": ["-y", "@playwright/mcp@latest"]}, "Sequential thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}, "Context 7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"]}, "memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"], "env": {"MEMORY_FILE_PATH": "D:/dev-storage/mcp_memory/gemini_memory.json"}}}}