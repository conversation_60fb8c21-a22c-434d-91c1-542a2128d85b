<!-- src/routes/+layout.svelte -->
<script lang="ts">
	import { onMount } from 'svelte';
	import { browser } from '$app/environment';
	import { initTelegramApp, isTelegramEnvironment } from '$lib/telegram/init';
	import { App } from 'konsta/svelte';
	import '../app.css';

	let telegramInitialized = false;
	let telegramComponents: any = null;

	onMount(async () => {
		if (browser) {
			try {
				// 检查是否在Telegram环境中
				if (isTelegramEnvironment()) {
					console.log('🚀 Initializing Telegram Mini App...');
					telegramComponents = await initTelegramApp();
					telegramInitialized = true;
					
					// 设置视口
					if (telegramComponents?.viewport) {
						telegramComponents.viewport.expand();
					}
				} else {
					console.log('⚠️ Not running in Telegram environment');
					telegramInitialized = true; // 允许继续运行
				}
			} catch (error) {
				console.error('❌ Failed to initialize Telegram:', error);
				telegramInitialized = true; // 允许继续运行，但可能功能受限
			}
		}
	});
</script>

<svelte:head>
	<title>CipherX</title>
	<meta name="description" content="CipherX - Kink Community Platform" />
</svelte:head>

{#if telegramInitialized}
	<App theme="ios">
		<slot />
	</App>
{:else}
	<!-- 加载中状态 -->
	<div class="flex h-screen items-center justify-center">
		<div class="text-center">
			<div class="mb-4 text-lg font-semibold">Initializing CipherX...</div>
			<div class="h-8 w-8 animate-spin rounded-full border-4 border-blue-500 border-t-transparent mx-auto"></div>
		</div>
	</div>
{/if}
