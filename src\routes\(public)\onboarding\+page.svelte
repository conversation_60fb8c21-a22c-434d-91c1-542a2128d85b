<!-- src/routes/(public)/onboarding/+page.svelte -->
<script lang="ts">
	import { superForm } from 'sveltekit-superforms';
	import { zodClient } from 'sveltekit-superforms/adapters';
	import { basicProfileSchema } from '$lib/schemas/profile.schema';
	import * as m from '$lib/paraglide/messages';
	import { page } from '$app/stores';

	export let data;

	const { form, errors, constraints, enhance, message } = superForm(data.form, {
		validators: zodClient(basicProfileSchema)
	});
</script>

<div class="container mx-auto max-w-lg p-4">
	<h1 class="mb-4 text-2xl font-bold">{m.onboarding_title()}</h1>
	<p class="mb-6 text-gray-600">{m.onboarding_description()}</p>

	<form method="POST" use:enhance>
		<!-- Nickname -->
		<div class="mb-4">
			<label for="nickname" class="mb-1 block font-medium">{m.nickname_label()}</label>
			<input
				type="text"
				id="nickname"
				name="nickname"
				bind:value={$form.nickname}
				aria-invalid={$errors.nickname ? 'true' : undefined}
				class="w-full rounded-md border p-2"
				data-fs-error-for="nickname"
				{...$constraints.nickname}
			/>
			{#if $errors.nickname}
				<div class="mt-1 text-sm text-red-500">{$errors.nickname}</div>
			{/if}
		</div>

		<!-- Age -->
		<div class="mb-4">
			<label for="age" class="mb-1 block font-medium">{m.age_label()}</label>
			<input
				type="number"
				id="age"
				name="age"
				bind:value={$form.age}
				aria-invalid={$errors.age ? 'true' : undefined}
				class="w-full rounded-md border p-2"
				data-fs-error-for="age"
				{...$constraints.age}
			/>
			{#if $errors.age}
				<div class="mt-1 text-sm text-red-500">{$errors.age}</div>
			{/if}
		</div>

		<!-- Height -->
		<div class="mb-4">
			<label for="heightCm" class="mb-1 block font-medium">{m.height_label()}</label>
			<input
				type="number"
				id="heightCm"
				name="heightCm"
				bind:value={$form.heightCm}
				aria-invalid={$errors.heightCm ? 'true' : undefined}
				class="w-full rounded-md border p-2"
				data-fs-error-for="heightCm"
				{...$constraints.heightCm}
			/>
			{#if $errors.heightCm}
				<div class="mt-1 text-sm text-red-500">{$errors.heightCm}</div>
			{/if}
		</div>

		<!-- Weight -->
		<div class="mb-4">
			<label for="weightKg" class="mb-1 block font-medium">{m.weight_label()}</label>
			<input
				type="number"
				id="weightKg"
				name="weightKg"
				bind:value={$form.weightKg}
				aria-invalid={$errors.weightKg ? 'true' : undefined}
				class="w-full rounded-md border p-2"
				data-fs-error-for="weightKg"
				{...$constraints.weightKg}
			/>
			{#if $errors.weightKg}
				<div class="mt-1 text-sm text-red-500">{$errors.weightKg}</div>
			{/if}
		</div>

		<!-- Body Type -->
		<div class="mb-4">
			<label for="bodyType" class="mb-1 block font-medium">{m.body_type_label()}</label>
			<select
				id="bodyType"
				name="bodyType"
				bind:value={$form.bodyType}
				aria-invalid={$errors.bodyType ? 'true' : undefined}
				class="w-full rounded-md border p-2"
				data-fs-error-for="bodyType"
				required
			>
				<option value="" disabled selected>{m.select_body_type_placeholder()}</option>
				{#each data.bodyTypes as type}
					<option value={type}>{bodyTypeLabels[type]()}</option>
				{/each}
			</select>
			{#if $errors.bodyType}
				<div class="mt-1 text-sm text-red-500">{$errors.bodyType}</div>
			{/if}
		</div>

		<!-- Country & Province -->
		<!-- Note: A real-world implementation would use a dedicated country/province selector component -->
		<div class="grid grid-cols-1 gap-4 md:grid-cols-2">
			<div class="mb-4">
				<label for="countryCode" class="mb-1 block font-medium">{m.country_label()}</label>
				<input
					type="text"
					id="countryCode"
					name="countryCode"
					bind:value={$form.countryCode}
					class="w-full rounded-md border p-2"
					{...$constraints.countryCode}
				/>
			</div>
			<div class="mb-4">
				<label for="provinceCode" class="mb-1 block font-medium">{m.province_label()}</label>
				<input
					type="text"
					id="provinceCode"
					name="provinceCode"
					bind:value={$form.provinceCode}
					class="w-full rounded-md border p-2"
					{...$constraints.provinceCode}
				/>
			</div>
		</div>
		
		<!-- City -->
		<div class="mb-4">
			<label for="city" class="mb-1 block font-medium">{m.city_label()}</label>
			<input
				type="text"
				id="city"
				name="city"
				bind:value={$form.city}
				class="w-full rounded-md border p-2"
				{...$constraints.city}
			/>
		</div>


		{#if $message}
			<div class="mb-4 rounded-md bg-red-100 p-4 text-red-700">{$message}</div>
		{/if}

		<button type="submit" class="w-full rounded-md bg-blue-600 p-3 text-white hover:bg-blue-700">
			{m.onboarding_submit_button()}
		</button>
	</form>
</div>
