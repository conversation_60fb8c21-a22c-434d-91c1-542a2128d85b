{"name": "cipherX", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync", "lint": "prettier --check . && eslint .", "format": "prettier --write .", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "db:generate": "drizzle-kit generate:pg", "db:migrate": "drizzle-kit migrate", "db:push": "drizzle-kit push:pg", "db:studio": "drizzle-kit studio", "script:run": "tsx"}, "devDependencies": {"@eslint/compat": "^1.2.5", "@eslint/js": "^9.18.0", "@internationalized/date": "^3.8.1", "@lucide/svelte": "^0.523.0", "@playwright/test": "^1.53.1", "@sveltejs/adapter-node": "^5.2.12", "@sveltejs/kit": "^2.22.2", "@sveltejs/vite-plugin-svelte": "^5.0.0", "@tailwindcss/forms": "^0.5.9", "@tailwindcss/typography": "^0.5.15", "@tailwindcss/vite": "^4.0.0", "@types/node": "^22", "@types/pg": "^8.15.4", "autoprefixer": "^10.4.21", "drizzle-kit": "^0.31.4", "drizzle-orm": "^0.44.2", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-svelte": "^3.0.0", "formsnap": "^2.0.1", "globals": "^16.0.0", "postcss": "^8.5.6", "prettier": "^3.6.2", "prettier-plugin-svelte": "^3.4.0", "prettier-plugin-tailwindcss": "^0.6.13", "svelte": "^5.0.0", "svelte-check": "^4.2.2", "tailwindcss": "^4.1.10", "tsx": "^4.20.3", "tw-animate-css": "^1.3.4", "typescript": "^5.0.0", "typescript-eslint": "^8.20.0", "vite": "^6.3.5", "vite-plugin-devtools-json": "^0.2.0"}, "dependencies": {"@auth/sveltekit": "^1.10.0", "@google/gemini-cli": "^0.1.7", "@inlang/paraglide-js": "^2.0.0", "@lottiefiles/dotlottie-web": "^0.47.0", "@lucia-auth/adapter-drizzle": "^1.1.0", "@node-rs/argon2": "^2.0.2", "@oslojs/crypto": "^1.0.1", "@oslojs/encoding": "^1.1.0", "@telegram-apps/bridge": "^2.9.0", "@telegram-apps/sdk": "^3.11.0", "@telegram-apps/sdk-svelte": "^2.0.26", "@telegram-apps/signals": "^1.1.2", "@tonconnect/ui": "^2.2.0", "@vitejs/plugin-basic-ssl": "^2.0.0", "country-state-city": "^3.2.1", "croner": "^9.1.0", "dotenv": "^16.6.0", "grammy": "^1.36.3", "konsta": "^4.0.1", "lucia": "^3.2.2", "nanoid": "^5.1.5", "pg": "^8.16.2", "postgres": "^3.4.5", "sveltekit-superforms": "^2.26.1", "vite-plugin-mkcert": "^1.17.8", "zod": "^3.25.67"}, "pnpm": {"onlyBuiltDependencies": ["esbuild"]}}