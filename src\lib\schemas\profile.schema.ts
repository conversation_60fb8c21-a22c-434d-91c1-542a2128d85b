// src/lib/schemas/profile.schema.ts
// CipherX TMA - 用户资料验证规则 (v2.1 - 最终版)

import { z } from 'zod';
import {
	createIdSchema,
	telegramUserIdSchema,
	ageSchema,
	heightSchema,
	weightSchema,
	countryCodeSchema,
	provinceCodeSchema,
	citySchema,
	nicknameSchema,
	bioSchema,
	telegramUsernameSchema,
	languageCodeSchema,
	tonWalletAddressSchema,
	kinkMapCodeSchema,
	kinkRatingValueSchema
} from './common.schema';

// 导入常量和枚举作为验证的“唯一事实来源”
import { KINK_ROLES, KINK_INTERESTS } from '$lib/constants/kinks';
import {
	orientationEnum,
	bodyTypeEnum,
	presentationStyleEnum,
	relationshipStatusEnum
} from '$lib/server/db/schema/_enums';

/**
 * 基础资料验证 Schema
 * 定义了用户解锁核心功能所必须提供的最小数据集。
 * 在 Onboarding 流程中，这张“表单”必须被完整填写。
 */
export const basicProfileSchema = z.object({
	nickname: nicknameSchema,
	age: ageSchema,
	heightCm: heightSchema,
	weightKg: weightSchema,
	bodyType: z.enum(bodyTypeEnum.enumValues, {
		required_error: 'error_body_type_required'
	}),
	countryCode: countryCodeSchema,
	provinceCode: provinceCodeSchema.optional().nullable(),
	city: citySchema.optional().nullable()
});

/**
 * 高级资料验证 Schema
 * 用户自愿填写的深度信息，所有字段均为可选。
 */
export const advancedProfileSchema = z.object({
	orientation: z.enum(orientationEnum.enumValues).optional().nullable(),
	presentationStyle: z.enum(presentationStyleEnum.enumValues).optional().nullable(),
	relationshipStatus: z.enum(relationshipStatusEnum.enumValues).optional().nullable(),
	bio: bioSchema.optional().nullable(),

	kinkRoles: z.array(z.enum(Object.keys(KINK_ROLES) as [string, ...string[]])).optional(),

	kinkRatings: z
		.record(z.enum(KINK_INTERESTS), kinkRatingValueSchema) // ✅ 使用平铺后的一维数组
		.optional()
		.nullable()
});

/**
 * 用户注册时，写入数据库所需的最少数据。
 * 由后端在 initData 验证成功后内部调用。
 */
export const userRegistrationSchema = z.object({
	id: createIdSchema('usr_'),
	telegramUserId: telegramUserIdSchema,
	kinkMapCode: kinkMapCodeSchema,
	nickname: nicknameSchema, // 从 initData 或后续步骤获取
	telegramUsername: telegramUsernameSchema.optional().nullable(),
	languageCode: languageCodeSchema.optional().nullable(),
	// ✅ 邀请者ID是可选的
	inviterId: createIdSchema('usr_').optional().nullable()
});

/**
 * 用于“编辑资料”的验证 Schema。
 * 它合并了基础和高级资料，并将所有字段设为可选，
 * 同时禁止客户端修改核心ID。
 */
export const updateUserProfileSchema = basicProfileSchema
	.merge(advancedProfileSchema)
	.partial() // .partial() 让所有字段都变为可选
	.extend({
		// 允许在更新时，也更新一些之前未填写的系统字段
		telegramUsername: telegramUsernameSchema.optional().nullable(),
		profileImageUrl: z.string().url({ message: 'error_url_invalid' }).optional().nullable()
	});

// --- 类型导出 ---
export type BasicProfileData = z.infer<typeof basicProfileSchema>;
export type AdvancedProfileData = z.infer<typeof advancedProfileSchema>;
export type UserRegistrationData = z.infer<typeof userRegistrationSchema>;
export type UpdateUserProfileData = z.infer<typeof updateUserProfileSchema>;
