// src/lib/server/services/user.service.ts
import { db } from '$lib/server/db';
import { users } from '$lib/server/db/schema';
import type { UpdateUserProfileData } from '$lib/schemas/profile.schema';
import { eq } from 'drizzle-orm';
import type { User } from 'lucia';

/**
 * Updates a user's profile in the database.
 * This function is the single source of truth for profile updates.
 *
 * @param userId - The ID of the user to update.
 * @param data - The validated data from the update form.
 * @returns An object indicating success or failure.
 */
export async function updateUserProfile(userId: string, data: UpdateUserProfileData): Promise<{ success: boolean; error?: string }> {
	try {
		// The data is already validated by Superforms, so we can trust it.
		// The trigger in the database will automatically handle the `updated_at` field.
		const [updatedUser] = await db
			.update(users)
			.set(data)
			.where(eq(users.id, userId))
			.returning({ id: users.id });

		if (!updatedUser) {
			return { success: false, error: 'error_user_not_found' };
		}

		return { success: true };
	} catch (error) {
		// In a real app, you'd want to log this error to a service like Sentry
		console.error(`Failed to update profile for user ${userId}:`, error);
		return { success: false, error: 'error_database_update_failed' };
	}
}

/**
 * Fetches the full user profile required for the onboarding form.
 * @param user - The minimal user object from Lucia's session.
 * @returns The full user profile from the database, or null if not found.
 */
export async function getFullUserProfile(user: User) {
    const userProfile = await db.query.users.findFirst({
        where: eq(users.id, user.id)
    });
    return userProfile ?? null;
}
